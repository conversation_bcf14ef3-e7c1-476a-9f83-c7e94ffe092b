// Export all stores and their utilities

import { useChatStore } from './chatStore';
import { useConnectionStore } from './connectionStore';
import { useThemeStore } from './themeStore';

// Chat Store
export {
  useChatStore,
  useChatMessages,
  useChatTyping,
  useChatViewMode,
  useChatMinimized,
  useChatUnreadCount,
} from './chatStore';

// Theme Store
export {
  useThemeStore,
  useThemeMode,
  useEffectiveTheme,
  useSystemPreference,
  useThemeActions,
  useThemeInitialization,
  getThemeIcon,
  getThemeLabel,
  getNextTheme,
  getCSSCustomProperty,
  setCSSCustomProperty,
} from './themeStore';

// Connection Store
export {
  useConnectionStore,
  useConnectionStatus,
  useConnectionState,
  useConnectionActions,
  getConnectionStatusColor,
  getConnectionStatusLabel,
  getConnectionStatusIcon,
  isConnectionHealthy,
  formatConnectionUptime,
} from './connectionStore';

// Store initialization and cleanup utilities
export const initializeStores = () => {
  // Initialize theme store
  const themeStore = useThemeStore.getState();
  const effectiveTheme = themeStore.getEffectiveTheme();
  
  // Apply initial theme
  if (typeof document !== 'undefined') {
    const root = document.documentElement;
    if (effectiveTheme === 'dark') {
      root.setAttribute('data-theme', 'dark');
      root.classList.add('dark');
    } else {
      root.removeAttribute('data-theme');
      root.classList.remove('dark');
    }
  }
};

// Store reset utilities
export const resetAllStores = () => {
  // Reset chat store
  useChatStore.getState().clearMessages();
  useChatStore.setState({
    isTyping: false,
    isMinimized: false,
    unreadCount: 0,
    viewMode: 'floating',
  });
  
  // Reset connection store
  useConnectionStore.setState({
    reconnectAttempts: 0,
    reconnectDelay: 1000,
    isManualDisconnect: false,
    lastPingTime: null,
  });
};

// Store persistence utilities (localStorage persistence removed for chat store)
export const clearPersistedData = () => {
  if (typeof localStorage !== 'undefined') {
    // Only clear theme store persistence, chat store no longer uses localStorage
    localStorage.removeItem('theme-store');
  }
};

// Store debugging utilities (development only)
export const getStoreStates = () => {
  if (!import.meta.env.DEV) {
    // eslint-disable-next-line no-console
    console.warn('Store debugging is only available in development mode');
    return {};
  }
  
  return {
    chat: useChatStore.getState(),
    theme: useThemeStore.getState(),
    connection: useConnectionStore.getState(),
  };
};

export const logStoreStates = () => {
  if (!import.meta.env.DEV) {
    return;
  }
  
  // eslint-disable-next-line no-console
  console.group('Store States');
  // eslint-disable-next-line no-console
  console.log('Chat Store:', useChatStore.getState());
  // eslint-disable-next-line no-console
  console.log('Theme Store:', useThemeStore.getState());
  // eslint-disable-next-line no-console
  console.log('Connection Store:', useConnectionStore.getState());
  // eslint-disable-next-line no-console
  console.groupEnd();
};

// Store subscription utilities
export const subscribeToStoreChanges = () => {
  if (!import.meta.env.DEV) {
    return () => {};
  }
  
  const unsubscribeFunctions: (() => void)[] = [];
  
  // Subscribe to chat store changes
  const unsubscribeChat = useChatStore.subscribe(
    () => {
      // Store change tracking can be added here if needed
    }
  );
  unsubscribeFunctions.push(unsubscribeChat);

  // Subscribe to theme store changes
  const unsubscribeTheme = useThemeStore.subscribe(
    () => {
      // Store change tracking can be added here if needed
    }
  );
  unsubscribeFunctions.push(unsubscribeTheme);

  // Subscribe to connection store changes
  const unsubscribeConnection = useConnectionStore.subscribe(
    () => {
      // Store change tracking can be added here if needed
    }
  );
  unsubscribeFunctions.push(unsubscribeConnection);
  
  // Return cleanup function
  return () => {
    unsubscribeFunctions.forEach((unsubscribe) => unsubscribe());
  };
};

// Store middleware for analytics/logging
export const createAnalyticsMiddleware = (trackEvent: (event: string, data: Record<string, unknown>) => void) => {
  // Simplified analytics middleware to avoid complex typing
  return <T>(config: T): T => {
    // Track that analytics middleware was applied
    trackEvent('analytics_middleware_applied', {
      timestamp: new Date().toISOString(),
    });
    return config;
  };
};

// Store performance monitoring
export const createPerformanceMiddleware = () => {
  // Simplified performance middleware to avoid complex typing
  return <T>(config: T): T => {
    // Performance monitoring can be added here if needed
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.log('Performance middleware applied');
    }
    return config;
  };
};
