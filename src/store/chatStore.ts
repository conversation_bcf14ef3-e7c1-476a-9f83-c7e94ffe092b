import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { ViewMode } from '../types/chat';
import { VIEW_MODE } from '../types/chat';

// Simplified Message Interface
interface SimpleMessage {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
}

// Simplified Chat Store Interface
interface ChatStore {
  // State
  messages: SimpleMessage[];
  isTyping: boolean;
  viewMode: ViewMode;
  isMinimized: boolean;
  unreadCount: number;
  currentThreadId: string | null; // Current conversation thread ID

  // Actions
  addMessage: (content: string, sender: 'user' | 'agent') => void;
  clearMessages: () => void;
  setTyping: (isTyping: boolean) => void;
  setViewMode: (mode: ViewMode) => void;
  setMinimized: (minimized: boolean) => void;
  markAsRead: () => void;
  setThreadId: (threadId: string | null) => void;
  clearConversation: () => void; // Clear messages and reset thread ID
}

// Generate unique ID for messages
const generateMessageId = (): string => {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Create the simplified chat store
export const useChatStore = create<ChatStore>()(
  devtools(
    (set) => ({
        // Initial state
        messages: [],
        isTyping: false,
        viewMode: VIEW_MODE.FLOATING,
        isMinimized: false,
        unreadCount: 0,
        currentThreadId: null,

        // Actions
        addMessage: (content: string, sender: 'user' | 'agent') => {
          const message: SimpleMessage = {
            id: generateMessageId(),
            content,
            sender,
            timestamp: new Date(),
          };

          // console.log('Adding message to store:', { content, sender });

          set((state) => ({
            messages: [...state.messages, message],
            // Increment unread count if it's an agent message and chat is minimized
            unreadCount:
              sender === 'agent' && state.isMinimized
                ? state.unreadCount + 1
                : state.unreadCount,
          }));
        },

        clearMessages: () => {
          set({ messages: [], unreadCount: 0 });
        },

        setTyping: (isTyping) => {
          set({ isTyping });
        },

        setViewMode: (viewMode) => {
          set({ viewMode });
        },

        setMinimized: (isMinimized) => {
          set((state) => ({
            isMinimized,
            // Clear unread count when opening chat
            unreadCount: isMinimized ? state.unreadCount : 0,
          }));
        },

        markAsRead: () => {
          set({ unreadCount: 0 });
        },

        setThreadId: (threadId) => {
          console.log('Setting threadId:', threadId);
          set({ currentThreadId: threadId });
        },

        clearConversation: () => {
          console.log('Clearing conversation and threadId');
          set({
            messages: [],
            unreadCount: 0,
            currentThreadId: null,
            isTyping: false
          });
        },
      }),
    {
      name: 'chat-store',
    }
  )
);

// Simplified selectors
export const useChatMessages = () => useChatStore((state) => state.messages);
export const useChatTyping = () => useChatStore((state) => state.isTyping);
export const useChatViewMode = () => useChatStore((state) => state.viewMode);
export const useChatMinimized = () => useChatStore((state) => state.isMinimized);
export const useChatUnreadCount = () => useChatStore((state) => state.unreadCount);
export const useChatThreadId = () => useChatStore((state) => state.currentThreadId);
