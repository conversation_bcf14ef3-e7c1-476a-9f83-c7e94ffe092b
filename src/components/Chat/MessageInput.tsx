import React, { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Send} from 'lucide-react';
import type { MessageInputProps } from '../../types/chat';
import clsx from 'clsx';

// Form data type without validation
type MessageFormData = {
  message: string;
};

const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = 'Type your message...',
  maxLength = 2000,
  className,
  children,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { isSubmitting },
  } = useForm<MessageFormData>({
    mode: 'onChange', // Ensure React Hook Form tracks changes immediately
    defaultValues: {
      message: '',
    },
  });

  // Get the register result once to avoid multiple calls
  const messageRegister = register('message');

  const messageValue = watch('message');
  const characterCount = messageValue?.length || 0;
  const isNearLimit = characterCount > maxLength * 0.8;
  const isOverLimit = characterCount > maxLength;
  
  // Auto-resize textarea
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;
    
    textarea.style.height = 'auto';
    const scrollHeight = textarea.scrollHeight;
    const maxHeight = 120; // Max height in pixels
    
    if (scrollHeight > maxHeight) {
      textarea.style.height = `${maxHeight}px`;
      textarea.style.overflowY = 'auto';
    } else {
      textarea.style.height = `${scrollHeight}px`;
      textarea.style.overflowY = 'hidden';
    }
    
    setIsExpanded(scrollHeight > 40); // Single line height
  };
  
  useEffect(() => {
    adjustTextareaHeight();
  }, [messageValue]);
  
  const onSubmit = async (data: MessageFormData) => {
    if (disabled || isOverLimit) return;

    // Get the message from form data
    const message = data.message?.trim() || '';

    // Don't send empty messages
    if (!message) return;

    try {
      await onSendMessage(message);
      reset();
      setIsExpanded(false);

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to send message:', error);
    }
  };
  
  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      // Submit the form using React Hook Form
      handleSubmit(onSubmit)();
    }
  };
  
  const handlePaste = (event: React.ClipboardEvent<HTMLTextAreaElement>) => {
    // Handle file paste in the future
    const items = event.clipboardData?.items;
    if (items) {
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.indexOf('image') !== -1) {
          // Handle image paste in the future
          // eslint-disable-next-line no-console
          console.log('Image pasted:', item);
        }
      }
    }
  };
  
  const canSend = !disabled && !isOverLimit && !isSubmitting;
  
  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className={clsx(
        'message-input',
        {
          'message-input--expanded': isExpanded,
          'message-input--disabled': disabled,
          'message-input--error': isOverLimit,
        },
        className
      )}
    >
      <div className="message-input__container">
        <div className="message-input__field-wrapper">
          <textarea
            {...messageRegister}
            ref={(e) => {
              // Store reference for our custom functionality
              textareaRef.current = e;
              // Call React Hook Form's ref
              if (messageRegister.ref) {
                messageRegister.ref(e);
              }
            }}
            placeholder={placeholder}
            disabled={disabled}
            maxLength={maxLength}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            className="message-input__field"
            rows={1}
            aria-label="Type your message"
            aria-describedby={
              isNearLimit ? 'character-count' : undefined
            }
          />
          
          <div className="message-input__actions">
            <button
              type="submit"
              className={clsx(
                'message-input__send',
                {
                  'message-input__send--active': canSend,
                }
              )}
              disabled={!canSend}
              aria-label="Send message"
            >
              <Send size={18} />
            </button>
          </div>
        </div>
        
        <div className="message-input__footer">
          {isNearLimit && (
            <div
              id="character-count"
              className={clsx(
                'message-input__character-count',
                {
                  'message-input__character-count--warning': isNearLimit && !isOverLimit,
                  'message-input__character-count--error': isOverLimit,
                }
              )}
            >
              {characterCount}/{maxLength}
            </div>
          )}

          {!isNearLimit && (
            <div className="message-input__hint">
              Press Enter to send, Shift+Enter for new line
            </div>
          )}

          {/* Debug info - remove in production */}
          <div style={{ fontSize: '10px', color: '#666', marginTop: '4px' }}>
            Debug: Form value = "{messageValue}" (length: {characterCount})
          </div>
        </div>
      </div>
      
      {children}
    </form>
  );
};

export default MessageInput;
