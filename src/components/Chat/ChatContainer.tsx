import React, { useEffect } from 'react';
import { useChatStore } from '../../store';
import { useSendMessage } from '../../hooks/api';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import FloatingChatButton from './FloatingChatButton';
import { Maximize2, Minimize2, Square } from 'lucide-react';
import clsx from 'clsx';
import { VIEW_MODE, type ChatContainerProps } from '../../types';
import edwinImage from '../../assets/edwin.png';

const ChatContainer: React.FC<ChatContainerProps> = ({
  viewMode: propViewMode = "fullpage",
  onMinimize,
  className,
  children,
}) => {
  const {
    messages,
    isTyping,
    viewMode: storeViewMode,
    unreadCount,
    currentThreadId,
    setViewMode,
    markAsRead,
  } = useChatStore();

  const sendMessageMutation = useSendMessage();
  
  const currentViewMode = propViewMode || storeViewMode;
  
  // Mark messages as read when chat is opened
  useEffect(() => {
    if (currentViewMode !== VIEW_MODE.MINIMIZED && unreadCount > 0) {
      markAsRead();
    }
  }, [currentViewMode, unreadCount, markAsRead]);

  // Handle body scroll and app container based on view mode
  useEffect(() => {
    const body = document.body;
    const html = document.documentElement;
    const appElement = document.querySelector('.app');

    if (currentViewMode === VIEW_MODE.FULLPAGE) {
      // Disable body scroll in fullpage mode
      body.style.overflow = 'hidden';
      html.style.overflow = 'hidden';
      appElement?.classList.remove('scrollable');
    } else {
      // Enable body scroll in chatbox and minimized modes
      body.style.overflow = 'auto';
      html.style.overflow = 'auto';
      appElement?.classList.add('scrollable');
    }

    // Cleanup on unmount
    return () => {
      body.style.overflow = '';
      html.style.overflow = '';
      appElement?.classList.remove('scrollable');
    };
  }, [currentViewMode]);
  
  const handleSendMessage = async (content: string) => {
    try {
      // Send message via API with correct payload structure
      await sendMessageMutation.mutateAsync({
        message: content,
        accountId: '2',
        customerId: '********',
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to send message:', error);
      // The mutation will handle updating the message status to error
    }
  };
  // Handler functions for the 3-state system
  const handleMinimizeToLogo = () => {
    setViewMode(VIEW_MODE.MINIMIZED);
    onMinimize?.();
  };

  const handleToChatbox = () => {
    setViewMode(VIEW_MODE.CHATBOX);
  };

  const handleToFullpage = () => {
    setViewMode(VIEW_MODE.FULLPAGE);
  };

  const handleRestoreFromMinimized = () => {
    // When clicking the floating button, restore to chatbox mode
    setViewMode(VIEW_MODE.CHATBOX);
  };

  // State checks
  const isFullpage = currentViewMode === VIEW_MODE.FULLPAGE;
  const isChatbox = currentViewMode === VIEW_MODE.CHATBOX;
  const isMinimized = currentViewMode === VIEW_MODE.MINIMIZED;

  // Don't render anything when minimized - only the floating button will show
  if (isMinimized) {
    return (
      <FloatingChatButton
        isVisible={true}
        onClick={handleRestoreFromMinimized}
        unreadCount={unreadCount}
      />
    );
  }

  return (
    <div
      className={clsx(
        'chat-container',
        {
          'chat-container--fullpage': isFullpage,
          'chat-container--chatbox': isChatbox,
        },
        className
      )}
    >
      {/* Header */}
      <div className="chat-container__header">
        <div className="chat-container__header-content">
          <div className="chat-container__title">
            <div className="chat-container__avatar">
              <img
                src={edwinImage}
                alt="Edwin - Eneco Assistant"
                className="chat-container__avatar-image"
              />
            </div>
            <div className="chat-container__title-text">
              <h2>Edwin</h2>
              <span className="chat-container__subtitle">Eneco Assistant</span>
            </div>
          </div>

          <div className="chat-container__header-actions">
            {/* State 1: Full Page Mode - show restore down and minimize buttons */}
            {isFullpage && (
              <>
                <button
                  onClick={handleToChatbox}
                  className="chat-container__action"
                  aria-label="Restore to chatbox"
                  title="Restore to chatbox"
                >
                  <Square size={18} />
                </button>
                <button
                  onClick={handleMinimizeToLogo}
                  className="chat-container__action"
                  aria-label="Minimize to logo"
                  title="Minimize to logo"
                >
                  <Minimize2 size={18} />
                </button>
              </>
            )}

            {/* State 2: Chatbox Mode - show maximize and minimize buttons */}
            {isChatbox && (
              <>
                <button
                  onClick={handleToFullpage}
                  className="chat-container__action"
                  aria-label="Maximize to full page"
                  title="Maximize to full page"
                >
                  <Maximize2 size={18} />
                </button>
                <button
                  onClick={handleMinimizeToLogo}
                  className="chat-container__action"
                  aria-label="Minimize to logo"
                  title="Minimize to logo"
                >
                  <Minimize2 size={18} />
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="chat-container__content">
        <div className="chat-container__messages">
          <MessageList
            messages={messages}
            isTyping={isTyping}
          />
        </div>

        <div className="chat-container__input">
          {/* Debug info - remove in production */}
          {currentThreadId && (
            <div style={{
              fontSize: '10px',
              color: '#666',
              padding: '4px 8px',
              background: '#f0f0f0',
              borderRadius: '4px',
              marginBottom: '8px'
            }}>
              Thread ID: {currentThreadId}
            </div>
          )}

          <MessageInput
            onSendMessage={handleSendMessage}
            disabled={sendMessageMutation.isPending}
            placeholder="Ask me anything about Eneco..."
          />
        </div>
      </div>

      {children}
    </div>
  );
};



export default ChatContainer;
