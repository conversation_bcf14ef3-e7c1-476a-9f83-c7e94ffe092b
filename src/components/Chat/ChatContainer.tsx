import React, { useEffect } from 'react';
import { useChatStore } from '../../store';
import { useSendMessage } from '../../hooks/api';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import FloatingChatButton from './FloatingChatButton';
import clsx from 'clsx';
import { VIEW_MODE, type ChatContainerProps } from '../../types';
import edwinImage from '../../assets/edwin.png';

const ChatContainer: React.FC<ChatContainerProps> = ({
  viewMode: propViewMode = "fullpage",
  onMinimize,
  className,
  children,
}) => {
  const {
    messages,
    isTyping,
    viewMode: storeViewMode,
    isMinimized,
    unreadCount,
    currentThreadId,
    setMinimized,
    markAsRead,
  } = useChatStore();

  const sendMessageMutation = useSendMessage();
  
  const currentViewMode = propViewMode || storeViewMode;
  
  // Mark messages as read when chat is opened
  useEffect(() => {
    if (!isMinimized && unreadCount > 0) {
      markAsRead();
    }
  }, [isMinimized, unreadCount, markAsRead]);
  
  const handleSendMessage = async (content: string) => {
    try {
      // Send message via API with correct payload structure
      await sendMessageMutation.mutateAsync({
        message: content,
        accountId: '2',
        customerId: '********',
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to send message:', error);
      // The mutation will handle updating the message status to error
    }
  };
  const handleMinimize = () => {
    setMinimized(true);
    onMinimize?.();
  };

  const handleRestore = () => {
    setMinimized(false);
  };

  const handleOpenChat = () => {
    if (isMinimized) {
      setMinimized(false);
    }
  };

  const isFloating = currentViewMode === VIEW_MODE.FLOATING;
  const isFullscreen = currentViewMode === VIEW_MODE.FULLSCREEN;
  const isFullpage = currentViewMode === VIEW_MODE.FULLPAGE;

  // Show floating button when chat is minimized or in certain view modes
  const showFloatingButton = isMinimized;
  
  return (
    <div
      className={clsx(
        'chat-container',
        {
          'chat-container--floating': isFloating,
          'chat-container--fullscreen': isFullscreen,
          'chat-container--fullpage': isFullpage,
          'chat-container--minimized': isMinimized,
        },
        className
      )}
    >
      {/* Header */}
      <div
        className="chat-container__header"
        onClick={isMinimized ? handleRestore : undefined}
        style={isMinimized ? { cursor: 'pointer' } : undefined}
      >
        <div className="chat-container__header-content">
          <div className="chat-container__title">
            <div className="chat-container__avatar">
              <img
                src={edwinImage}
                alt="Edwin - Eneco Assistant"
                className="chat-container__avatar-image"
              />
            </div>
            <div className="chat-container__title-text">
              <h2>Edwin</h2>
              <span className="chat-container__subtitle">Eneco Assistant</span>
            </div>
            {/* <ConnectionStatus /> */}
          </div>
          
          <div className="chat-container__header-actions">
            {/* Edwin logo button - always shown as minimize button */}
            <button
              onClick={isMinimized ? handleRestore : handleMinimize}
              className="chat-container__action chat-container__action--edwin-logo"
              aria-label={isMinimized ? "Restore chat" : "Minimize chat"}
            >
              <img
                src={edwinImage}
                alt="Edwin"
                className="chat-container__edwin-logo"
              />
            </button>
          </div>
        </div>
        
        {unreadCount > 0 && isMinimized && (
          <div className="chat-container__unread-badge">
            {unreadCount}
          </div>
        )}
      </div>
      
      {/* Main Content */}
      {!isMinimized && (
        <div className="chat-container__content">
          <div className="chat-container__messages">
            <MessageList
              messages={messages}
              isTyping={isTyping}
            />
          </div>
          
          <div className="chat-container__input">
            {/* Debug info - remove in production */}
            {currentThreadId && (
              <div style={{
                fontSize: '10px',
                color: '#666',
                padding: '4px 8px',
                background: '#f0f0f0',
                borderRadius: '4px',
                marginBottom: '8px'
              }}>
                Thread ID: {currentThreadId}
              </div>
            )}

            <MessageInput
              onSendMessage={handleSendMessage}
              disabled={sendMessageMutation.isPending}
              placeholder="Ask me anything about Eneco..."
            />
          </div>
        </div>
      )}
      
      {children}

      {/* Floating Chat Button - shown when chat is minimized */}
      <FloatingChatButton
        isVisible={showFloatingButton}
        onClick={handleOpenChat}
        unreadCount={unreadCount}
      />
    </div>
  );
};



export default ChatContainer;
