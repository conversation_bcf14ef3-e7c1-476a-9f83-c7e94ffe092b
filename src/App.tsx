import ChatContainer from './components/Chat/ChatContainer'
import QueryProvider from './providers/QueryProvider'

function App() {
  return (
    <QueryProvider>
      <div className="app">
        {/* Background content to demonstrate scrolling */}
        <div style={{
          padding: '2rem',
          minHeight: '200vh',
          background: 'linear-gradient(45deg, #f0f9ff, #e0f2fe)',
          position: 'relative',
          zIndex: 1
        }}>
          <h1 style={{ marginBottom: '2rem', color: '#1e40af' }}>Eneco Website Content</h1>
          <p style={{ marginBottom: '1rem', lineHeight: '1.6' }}>
            This is sample content to demonstrate that the background is scrollable when the chat is in chatbox mode.
          </p>
          <p style={{ marginBottom: '1rem', lineHeight: '1.6' }}>
            When the chat is in full page mode, this content should not be scrollable.
          </p>
          <p style={{ marginBottom: '1rem', lineHeight: '1.6' }}>
            When the chat is minimized to just the <PERSON> logo, this content should be fully scrollable.
          </p>

          {/* Add more content to make scrolling obvious */}
          {Array.from({ length: 20 }, (_, i) => (
            <div key={i} style={{
              margin: '2rem 0',
              padding: '1rem',
              background: 'white',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ marginBottom: '1rem', color: '#1e40af' }}>Section {i + 1}</h3>
              <p style={{ lineHeight: '1.6' }}>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
                incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
              </p>
            </div>
          ))}
        </div>

        <ChatContainer />
      </div>
    </QueryProvider>
  )
}

export default App
